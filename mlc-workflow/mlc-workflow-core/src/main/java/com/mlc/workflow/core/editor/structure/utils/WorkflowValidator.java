package com.mlc.workflow.core.editor.structure.utils;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.FlowNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.canvas.capability.*;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.traverse.AbstractNodeVisitor;
import com.mlc.workflow.core.editor.structure.traverse.TraverseContext;
import com.mlc.workflow.core.editor.structure.traverse.TraverseService;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 工作流结构校验器
 * 提交前的统一校验，确保流程结构的完整性和一致性
 */
@Slf4j
public class WorkflowValidator {
    
    /**
     * 校验结果类
     */
    @Getter
    public static class ValidationResult {
        private boolean valid;
        private final List<String> errors;
        private final List<String> warnings;
        
        public ValidationResult() {
            this.valid = true;
            this.errors = new ArrayList<>();
            this.warnings = new ArrayList<>();
        }
        
        public void addError(String error) {
            this.errors.add(error);
            this.valid = false;
        }
        
        public void addWarning(String warning) {
            this.warnings.add(warning);
        }
    }
    
    /**
     * 验证流程节点
     * @param processNode 流程节点
     * @return 验证结果
     */
    public ValidationResult validate(ProcessNode processNode) {
        ValidationResult result = new ValidationResult();
        
        if (processNode == null) {
            result.addError("流程节点不能为空");
            return result;
        }
        
        // 基础结构验证
        validateBasicStructure(processNode, result);

        // 基于遍历的结构验证（使用 TraverseService）
        validateStructureByTraversal(processNode, result);

        // 单流程单EndOwner验证
        validateSingleEndOwner(processNode, result);

        // 无悬空节点验证
        validateNoOrphanNodes(processNode, result);

        // 网关完整性验证
        validateGatewayIntegrity(processNode, result);

        // 条件完备性验证
        validateConditionCompleteness(processNode, result);
        
        // 无环验证
        validateNoCycles(processNode, result);
        
        // 子流程一致性验证
        validateSubProcessConsistency(processNode, result);
        
        return result;
    }
    
    /**
     * 验证基础结构
     */
    private void validateBasicStructure(ProcessNode processNode, ValidationResult result) {
        // 检查开始事件
        String startEventId = processNode.getStartEventId();
        if (startEventId == null || startEventId.trim().isEmpty()) {
            result.addError("流程缺少开始事件ID");
            return;
        }
        
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        if (flowNodeMap == null || flowNodeMap.isEmpty()) {
            result.addError("流程节点映射为空");
            return;
        }
        
        BaseNodeCanvas startEvent = flowNodeMap.get(startEventId);
        if (startEvent == null) {
            result.addError("找不到开始事件节点: " + startEventId);
        } else if (!(startEvent instanceof StartEventNodeCanvas)) {
            result.addError("开始事件节点类型错误: " + startEvent.getClass().getSimpleName());
        }
    }
    
    /**
     * 验证单流程单EndOwner
     * 根据设计方案：同一ProcessNode内，恰有1个节点的nextId=99
     */
    private void validateSingleEndOwner(ProcessNode processNode, ValidationResult result) {
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        List<String> endOwners = new ArrayList<>();

        for (BaseNodeCanvas node : flowNodeMap.values()) {
            if (node instanceof IRoutable routableNode) {
                if (EndOwnerManager.END_OWNER_ID.equals(routableNode.getNextId())) {
                    endOwners.add(node.getId());
                }
            }
        }

        // 根据设计方案：恰有1个节点的nextId=99
        if (endOwners.isEmpty()) {
            result.addError("流程缺少EndOwner（nextId=99的节点），违反EndOwner不变量");
        } else if (endOwners.size() > 1) {
            result.addError("流程有多个EndOwner: " + endOwners + "，违反EndOwner唯一性约束");
        } else {
            // 验证EndOwner的合理性
            String endOwnerId = endOwners.get(0);
            BaseNodeCanvas endOwnerNode = flowNodeMap.get(endOwnerId);

            // 检查EndOwner是否有前驱（除非是开始节点）
            if (!endOwnerId.equals(processNode.getStartEventId())) {
                List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, endOwnerId);
                if (prevNodes.isEmpty()) {
                    result.addWarning("EndOwner节点 " + endOwnerId + " 没有前驱节点，可能是孤立节点");
                }
            }

            log.debug("EndOwner验证通过: {}", endOwnerId);
        }
    }
    
    /**
     * 验证无悬空节点
     */
    private void validateNoOrphanNodes(ProcessNode processNode, ValidationResult result) {
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        String startEventId = processNode.getStartEventId();
        
        for (BaseNodeCanvas node : flowNodeMap.values()) {
            String nodeId = node.getId();

            // 跳过非可路由节点（如SystemNodeCanvas等配置节点）
            if (!(node instanceof IRoutable)) {
                log.debug("跳过非可路由节点: {} ({})", nodeId, node.getClass().getSimpleName());
                continue;
            }

            // 开始事件不需要前驱
            if (nodeId.equals(startEventId)) {
                continue;
            }

            // 分支叶子的前驱是网关，特殊处理
            if (node instanceof ConditionNodeCanvas) {
                IRoutable routableNode = (IRoutable) node;
                String prveId = routableNode.getPrveId();
                if (prveId == null || prveId.trim().isEmpty()) {
                    result.addError("分支叶子节点 " + nodeId + " 缺少前驱");
                } else {
                    BaseNodeCanvas prevNode = flowNodeMap.get(prveId);
                    log.info("验证分支叶子节点 {} 的前驱 {} (类型: {})", nodeId, prveId,
                            prevNode != null ? prevNode.getClass().getSimpleName() : "null");
                    if (!(prevNode instanceof GatewayNodeCanvas)) {
                        // 检查是否是扁平化的情况：前驱节点直接指向这个分支叶子
                        boolean isFlattened = false;
                        if (prevNode instanceof IRoutable routablePrev) {
                            String prevNextId = routablePrev.getNextId();
                            log.info("前驱节点 {} 的nextId: {}, 分支叶子ID: {}", prveId, prevNextId, nodeId);
                            if (nodeId.equals(prevNextId)) {
                                isFlattened = true;
                                log.info("分支叶子节点 {} 的前驱 {} 不是网关，但是前驱直接指向分支叶子，判定为扁平化情况",
                                         nodeId, prveId);
                            }
                        }

                        if (!isFlattened) {
                            log.info("分支叶子节点 {} 的前驱 {} 不是网关且不是扁平化情况，添加错误", nodeId, prveId);
                            result.addError("分支叶子节点 " + nodeId + " 的前驱不是网关");
                        }
                    }
                }
                continue;
            }

            // 其他可路由节点必须有前驱
            List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, nodeId);
            if (prevNodes.isEmpty()) {
                result.addError("节点 " + nodeId + " 没有前驱节点");
            }
        }
        
        // 检查所有nextId指向的节点是否存在
        for (BaseNodeCanvas node : flowNodeMap.values()) {
            if (node instanceof IRoutable routableNode) {
                String nextId = routableNode.getNextId();
                if (nextId != null && !nextId.trim().isEmpty() && 
                    !EndOwnerManager.END_OWNER_ID.equals(nextId) && !flowNodeMap.containsKey(nextId)) {
                    result.addError("节点 " + node.getId() + " 的nextId指向不存在的节点: " + nextId);
                }
            }
        }
    }
    
    /**
     * 验证网关完整性
     */
    private void validateGatewayIntegrity(ProcessNode processNode, ValidationResult result) {
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        
        for (BaseNodeCanvas node : flowNodeMap.values()) {
            if (node instanceof GatewayNodeCanvas gateway) {
                validateSingleGateway(processNode, gateway, result);
            }
        }
    }
    
    /**
     * 验证单个网关
     */
    private void validateSingleGateway(ProcessNode processNode, GatewayNodeCanvas gateway, ValidationResult result) {
        String gatewayId = gateway.getId();
        List<String> flowIds = gateway.getFlowIds();
        
        // 检查flowIds非空
        if (flowIds == null || flowIds.isEmpty()) {
            result.addError("网关 " + gatewayId + " 没有分支");
            return;
        }
        
        // 检查分支数量
        Integer gatewayType = gateway.getGatewayType();
        if (gatewayType != null && gatewayType == GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE) {
            if (flowIds.isEmpty()) {
                result.addError("唯一分支网关 " + gatewayId + " 至少需要1条分支");
            }
        } else if (gatewayType != null && gatewayType == GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL) {
            if (flowIds.size() < 2) {
                result.addWarning("并行分支网关 " + gatewayId + " 建议至少有2条分支");
            }
        }
        
        // 检查分支叶子是否存在
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        for (String flowId : flowIds) {
            BaseNodeCanvas branchLeaf = flowNodeMap.get(flowId);
            if (branchLeaf == null) {
                result.addError("网关 " + gatewayId + " 的分支叶子不存在: " + flowId);
            } else if (!(branchLeaf instanceof ConditionNodeCanvas)) {
                result.addError("网关 " + gatewayId + " 的分支叶子类型错误: " + branchLeaf.getClass().getSimpleName());
            }
        }
        
        // 使用网关语义策略验证
        if (!GatewaySemanticsStrategy.validateGatewaySemantics(processNode, gateway)) {
            result.addError("网关 " + gatewayId + " 语义验证失败");
        }
    }
    
    /**
     * 验证条件完备性（唯一分支）
     */
    private void validateConditionCompleteness(ProcessNode processNode, ValidationResult result) {
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        
        for (BaseNodeCanvas node : flowNodeMap.values()) {
            if (node instanceof GatewayNodeCanvas gateway &&
                gateway.getGatewayType() != null && gateway.getGatewayType() == GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE) {
                
                validateExclusiveGatewayConditions(processNode, gateway, result);
            }
        }
    }
    
    /**
     * 验证唯一分支网关的条件
     */
    private void validateExclusiveGatewayConditions(ProcessNode processNode, GatewayNodeCanvas gateway,
                                                  ValidationResult result) {
        String gatewayId = gateway.getId();
        List<String> flowIds = gateway.getFlowIds();
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        
        boolean hasElse = false;

        for (String flowId : flowIds) {
            BaseNodeCanvas branchLeaf = flowNodeMap.get(flowId);

            if (branchLeaf instanceof IHasConditions conditionNode) {
                if (!conditionNode.hasValidConditions()) {
                    result.addError("网关 " + gatewayId + " 的分支 " + flowId + " 缺少有效条件");
                }

                // 检查else条件
                if (isElseCondition(conditionNode)) {
                    if (hasElse) {
                        result.addError("网关 " + gatewayId + " 有多个else分支");
                    }
                    hasElse = true;
                }
            }
        }

        // 唯一分支网关建议有else分支
        if (!hasElse && flowIds.size() > 1) {
            result.addWarning("网关 " + gatewayId + " 建议设置else分支以处理其他情况");
        }
    }
    
    /**
     * 验证无环
     */
    private void validateNoCycles(ProcessNode processNode, ValidationResult result) {
        String startEventId = processNode.getStartEventId();
        Set<String> visited = new HashSet<>();
        Set<String> recursionStack = new HashSet<>();
        
        if (hasCycle(processNode, startEventId, visited, recursionStack)) {
            result.addError("流程中存在环路");
        }
    }
    
    /**
     * 检查是否有环路（DFS）
     */
    private boolean hasCycle(ProcessNode processNode, String nodeId, Set<String> visited, Set<String> recursionStack) {
        if (nodeId == null || EndOwnerManager.END_OWNER_ID.equals(nodeId)) {
            return false;
        }
        
        if (recursionStack.contains(nodeId)) {
            return true; // 发现环路
        }
        
        if (visited.contains(nodeId)) {
            return false; // 已访问过，无环
        }
        
        visited.add(nodeId);
        recursionStack.add(nodeId);
        
        BaseNodeCanvas node = processNode.getFlowNodeMap().get(nodeId);
        if (node == null) {
            recursionStack.remove(nodeId);
            return false;
        }
        
        // 检查普通的下一个节点
        if (node instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            if (hasCycle(processNode, nextId, visited, recursionStack)) {
                return true;
            }
        }
        
        // 检查网关的分支
        if (node instanceof IHasBranches branchNode) {
            List<String> flowIds = branchNode.getFlowIds();
            if (flowIds != null) {
                for (String flowId : flowIds) {
                    if (hasCycle(processNode, flowId, visited, recursionStack)) {
                        return true;
                    }
                }
            }
        }
        
        recursionStack.remove(nodeId);
        return false;
    }
    
    /**
     * 验证子流程一致性
     */
    private void validateSubProcessConsistency(ProcessNode processNode, ValidationResult result) {
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        
        for (BaseNodeCanvas node : flowNodeMap.values()) {
            if (node instanceof IHasSubProcess subProcessNode) {
                ProcessNode subProcess = subProcessNode.getProcessNode();
                if (subProcess != null) {
                    // 递归验证子流程
                    ValidationResult subResult = validate(subProcess);
                    if (!subResult.isValid()) {
                        result.addError("子流程 " + node.getId() + " 验证失败: " + 
                                      String.join(", ", subResult.getErrors()));
                    }
                }
            }
        }
    }

    /**
     * 检查是否为else条件
     * @param conditionNode 条件节点
     * @return 是否为else条件
     */
    private boolean isElseCondition(IHasConditions conditionNode) {
        // 简化实现：检查条件中是否包含"else"
        // 实际应该根据具体的条件结构来判断
        return conditionNode.getOperateCondition() != null &&
               conditionNode.getOperateCondition().stream()
                   .flatMap(List::stream)
                   .anyMatch(condition ->
                       "else".equals(condition.getFiledId()) ||
                       "else".equals(condition.getConditionId()));
    }

    /**
     * 基于遍历的结构验证
     * 使用 TraverseService 遍历整个流程，检查结构完整性
     * @param processNode 流程节点
     * @param result 验证结果
     */
    private void validateStructureByTraversal(ProcessNode processNode, ValidationResult result) {
        try {
            // 创建验证访问者
            ValidationVisitor visitor = new ValidationVisitor(result);

            // 使用 TraverseService 遍历流程
            TraverseService.visit(processNode, visitor);

            // 检查是否有节点被访问到
            if (visitor.getVisitedNodeCount() == 0) {
                result.addError("流程中没有可访问的节点");
            }

            log.debug("基于遍历的验证完成，访问了 {} 个节点", visitor.getVisitedNodeCount());

        } catch (Exception e) {
            result.addError("遍历验证时发生错误: " + e.getMessage());
            log.error("遍历验证失败", e);
        }
    }

    /**
     * 验证访问者
     * 在遍历过程中收集验证信息
     */
    private static class ValidationVisitor extends AbstractNodeVisitor {
        private final ValidationResult result;
        @Getter
        private int visitedNodeCount = 0;
        private final Set<String> visitedNodes = new HashSet<>();

        public ValidationVisitor(ValidationResult result) {
            this.result = result;
        }

        @Override
        public void visitFlowNode(FlowNodeCanvas node, TraverseContext context) {
            visitedNodeCount++;
            visitedNodes.add(node.getId());

            // 验证节点基本属性
            if (node.getId() == null || node.getId().trim().isEmpty()) {
                result.addError("发现节点ID为空的节点");
            }

            if (node.getName() == null || node.getName().trim().isEmpty()) {
                result.addWarning("节点 " + node.getId() + " 的名称为空");
            }
        }

        @Override
        public void visitGateway(GatewayNodeCanvas node, TraverseContext context) {
            super.visitGateway(node, context);

            // 验证网关特有属性
            if (node instanceof IHasBranches) {
                List<String> flowIds = node.getFlowIds();
                if (flowIds == null || flowIds.isEmpty()) {
                    result.addError("网关 " + node.getId() + " 没有分支");
                } else if (flowIds.size() < 2) {
                    result.addError("网关 " + node.getId() + " 分支数量不足（至少需要2个分支）");
                }
            }
        }

        @Override
        public void visitCondition(ConditionNodeCanvas node, TraverseContext context) {
            super.visitCondition(node, context);

            // 验证条件节点
            if (node instanceof IHasConditions) {
                if (!node.hasValidConditions()) {
                    result.addWarning("条件节点 " + node.getId() + " 缺少有效条件");
                }
            }
        }

        @Override
        public void startVisit(ProcessNode processNode, TraverseContext context) {
            // 验证开始事件
            String startEventId = processNode.getStartEventId();
            if (startEventId == null || startEventId.trim().isEmpty()) {
                result.addError("流程缺少开始事件ID");
            } else {
                BaseNodeCanvas startNode = processNode.getFlowNodeMap().get(startEventId);
                if (startNode == null) {
                    result.addError("开始事件节点不存在: " + startEventId);
                }
            }
        }

        @Override
        public void endVisit(ProcessNode processNode, TraverseContext context) {
            // 验证是否所有节点都被访问到
            Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
            Set<String> allNodeIds = flowNodeMap.keySet();
            Set<String> unvisitedNodes = new HashSet<>(allNodeIds);
            unvisitedNodes.removeAll(visitedNodes);

            if (!unvisitedNodes.isEmpty()) {
                result.addWarning("发现未访问到的节点: " + String.join(", ", unvisitedNodes));
            }
        }

    }
}
